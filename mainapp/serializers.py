from rest_framework import serializers
from mainapp.stripe_utils import get_user_plan_names_batch
from mainapp.models import (
    WebflowIntegration,
    Website,
    # ProTip,
    Article,
    ScheduleArticlePosting,
    AdminStats,
    User,
    AutomationProject,
    SerperResults,
    BackLink,
    AutoCoupon,
    WixIntegration,
    WordpressIntegration,
    WordpressCategories,
    ShopifyIntegration,
    WebPage,
    BlogFinder,
    BlogFinderProject,
    GlossaryContent,
    GuestPostFinderQuery,
    HypestatData,
    GuestPostFinderResult,
    RedditPostFinderQuery,
    RedditPostFinderResult,
    ChangeLog,
    GhostIntegration,
    AICalculator,
    GHLIntegration,
    GHLCategories,
    WordpressPublishedArticle,
    GoogleIntegration,
    AIStatsPage, 
    AIStatsPageVersion,
    AIComparisonPage,
    AIComparisonPageVersion,
    AIShareWidget,
)

from django.db import models
from django.utils.timesince import timesince
from django.utils import timezone
import datetime

# class ConnectedWebsitesSerializer(serializers.ModelSerializer):
#     class Meta:
#         model = Website
#         fields = ["domain", "protocol"]


# class ProTipSerializer(serializers.ModelSerializer):
#     tip_id = serializers.IntegerField(source="id")

#     class Meta:
#         model = ProTip
#         fields = ["tip_id", "text"]


class ArticleTitleTableDataSerializer(serializers.ModelSerializer):
    articleUID = serializers.CharField(source="article_uid", read_only=True)
    articleTitle = serializers.CharField(source="title", read_only=True)
    keyword = serializers.CharField(source="keyword.keyword", read_only=True)
    keywordHash = serializers.CharField(
        source="keyword.keyword_md5_hash", read_only=True
    )
    keywordTraffic = serializers.IntegerField(source="keyword.volume", read_only=True)
    internalLinks = serializers.IntegerField(source="internal_link_count")
    externalLinks = serializers.IntegerField(source="external_link_count")
    images = serializers.IntegerField(source="image_count", read_only=True)
    wordCount = serializers.IntegerField(source="word_count", read_only=True)
    isProcessing = serializers.BooleanField(source="is_processing", read_only=True)
    isGenerated = serializers.BooleanField(source="is_generated", read_only=True)
    isPosted = serializers.BooleanField(source="is_posted", read_only=True)
    isFailed = serializers.BooleanField(source="is_failed", read_only=True)
    isArchived = serializers.BooleanField(source="is_archived", read_only=True)
    postLink = serializers.CharField(source="article_link", read_only=True)
    isUserAdded = serializers.BooleanField(source="is_user_added", read_only=True)
    postedTo = serializers.CharField(source="posted_to", read_only=True)
    createdOn = serializers.DateTimeField(source="created_on", read_only=True)
    generatedOn = serializers.DateTimeField(source="generated_on", read_only=True)
    postedOn = serializers.DateTimeField(source="posted_on", read_only=True)
    feedback = serializers.CharField(read_only=True)
    KeywordProjectId = serializers.CharField(source="keyword_project_id", read_only=True)
    scheduledOn = serializers.DateTimeField(source="schedulearticleposting.schedule_on", read_only=True)
    scheduledDateTime = serializers.DateTimeField(source="schedulearticleposting.schedule_datetime", read_only=True)
    selectedIntegrationName = serializers.CharField(source="schedulearticleposting.selected_integration_name", read_only=True) 
    
    class Meta:
        model = Article
        fields = [
            "articleUID",
            "articleTitle",
            "keyword",
            "keywordHash",
            "keywordTraffic",
            "internalLinks",
            "externalLinks",
            "images",
            "wordCount",
            "isProcessing",
            "isGenerated",
            "isPosted",
            "isFailed",
            "isArchived",
            "postLink",
            "isUserAdded",
            "postedTo",
            "createdOn",
            "generatedOn",
            "feedback",
            "KeywordProjectId",
            "postedOn",
            "scheduledOn",
            "scheduledDateTime",
            "selectedIntegrationName"  
        ]


class KeywordDetailsTableDataSerializer(serializers.ModelSerializer):
    links = serializers.IntegerField(source="internal_link_count")
    externalLinks = serializers.IntegerField(source="external_link_count")
    images = serializers.IntegerField(source="image_count", read_only=True)
    isProcessing = serializers.BooleanField(source="is_processing", read_only=True)
    isGenerated = serializers.BooleanField(source="is_generated", read_only=True)
    isPosted = serializers.BooleanField(source="is_posted", read_only=True)

    class Meta:
        model = Article
        fields = [
            "article_uid",
            "title",
            "links",
            "externalLinks",
            "images",
            "isProcessing",
            "isGenerated",
            "isPosted",
        ]


class WebsiteListSerializer(serializers.ModelSerializer):
    class Meta:
        model = Website
        fields = ["domain", "logo_url"]


class AdminStatsSerializer(serializers.ModelSerializer):
    class Meta:
        model = AdminStats
        fields = [
            "total_signups",
            "total_paid_customer",
            "total_free_customers",
            "today_signup",
            "today_paid_users",
            "paid_users_weekly"
        ]


class AllAdminsSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ["id", "username", "email", "date_joined"]


class KeywordProjectsSerializer(serializers.Serializer):
    projectName = serializers.CharField()
    totalKeywords = serializers.IntegerField()
    totalTrafficVolume = serializers.IntegerField()
    dateCreated = serializers.CharField()
    projectId = serializers.CharField()
    locationIsoCode = serializers.CharField()
    mostRecentArtTitleTimestamp = serializers.CharField()

    class Meta:
        fields = [
            "projectName",
            "totalKeywords",
            "totalTrafficVolume",
            "dateCreated",
            "projectId",
            "locationIsoCode",
            "mostRecentArtTitleTimestamp",
        ]


class KeywordProjectKeywordsSerializer(serializers.Serializer):
    keyword = serializers.CharField()
    keywordHash = serializers.CharField()
    keywordTraffic = serializers.IntegerField()
    difficultyScore = serializers.DecimalField(max_digits=4, decimal_places=2)
    titlesGenerated = serializers.BooleanField()
    mostRecentArtTitleTimestamp = serializers.CharField()
    kwVolume = serializers.BooleanField()
    
    class Meta:
        fields = [
            "keyword",
            "keywordHash",
            "keywordTraffic",
            "difficultyScore",
            "titlesGenerated",
            "mostRecentArtTitleTimestamp",
            "kwVolume",
        ]


class AutomationProjectSerializer(serializers.ModelSerializer):
    projectName = serializers.CharField(source='project_name')
    projectId = serializers.CharField(source='project_id')
    automationState = serializers.CharField(source='auto_publish_state')
    selectedKeywordProjectName = serializers.CharField(source='associated_keyword_project.project_name')
    selectedKeywordProjectId = serializers.CharField(source='associated_keyword_project.project_id')
    trafficRangeMin = serializers.IntegerField(source='keywords_traffic_range_min')
    trafficRangeMax = serializers.IntegerField(source='keywords_traffic_range_max')
    articlesCount = serializers.IntegerField(source='article_count')
    frequency = serializers.CharField()
    integration = serializers.CharField(source='selected_integration_name')
    publishState = serializers.CharField(source='auto_publish_state')
    publishDays = serializers.ListField(source='auto_publish_days')
    publishTime = serializers.CharField(source='auto_publish_time')
    publishOnlyGeneratedArticles = serializers.BooleanField(source='publish_only_generated_articles')
    dateCreated = serializers.SerializerMethodField(source='created_on')
    integration_unique_id = serializers.CharField(source='selected_integration_unique_text_id')
    publishedArticlesCount = serializers.SerializerMethodField()
    scheduledArticleCount = serializers.SerializerMethodField()

    class Meta:
        model = AutomationProject
        fields = [
            "projectName",
            "projectId",
            "automationState",
            "selectedKeywordProjectName",
            "selectedKeywordProjectId",
            "trafficRangeMin",
            "trafficRangeMax",
            "articlesCount",
            "frequency",
            "integration",
            "publishState",
            "publishDays",
            "publishTime",
            "publishOnlyGeneratedArticles",
            "dateCreated",
            "integration_unique_id",
            "publishedArticlesCount",
            "scheduledArticleCount"
        ]

    def get_dateCreated(self, obj):
        return obj.created_on.strftime('%b %d, %Y')

    def get_scheduledArticleCount(self, obj):
        articles = Article.objects.filter(associated_automation_project=obj, is_generated=True, is_posted=False)
        return articles.count()

    def get_publishedArticlesCount(self, obj):
        return Article.objects.filter(associated_automation_project=obj, is_posted=True).count()



class SerperResultsSerializer(serializers.ModelSerializer):
    class Meta:
        model = SerperResults
        fields = ["result"]


class CSVUploadSerializer(serializers.Serializer):
    csv_file = serializers.FileField()


class BackLinkSerializer(serializers.ModelSerializer):
    class Meta:
        model = BackLink
        fields = '__all__'


class AutoCouponSerializer(serializers.ModelSerializer):
    class Meta:
        model = AutoCoupon
        fields = ['name', 'coupon_code', 'enabled']


class WebflowSitesSerializer(serializers.ModelSerializer):
    class Meta:
        model = WebflowIntegration
        fields = ['site_id', 'collection_id', 'site_url']


class WordpressSitesSerializer(serializers.ModelSerializer):
    class Meta:
        model = WordpressIntegration
        fields = ['site_url']


class WixSitesSerializer(serializers.ModelSerializer):
    class Meta:
        model = WixIntegration
        fields = ['site_id', 'site_url']

class WordpressCategoriesSerializer(serializers.ModelSerializer):
    """
    Serializer for the WordpressCategories model.
    """
    class Meta:
        model = WordpressCategories
        fields = ['article_title', 'category_id']

class ShopifyShopsSerializer(serializers.ModelSerializer):
    class Meta:
        model = ShopifyIntegration
        fields = ['shop_url']

class GhostSitesSerializer(serializers.ModelSerializer):
    class Meta:
        model = GhostIntegration
        fields = ['site_url']


class ArticleSerializer(serializers.ModelSerializer):
    user_email = serializers.EmailField(source='user.email')
    user_id = serializers.UUIDField(source='user.id')
    user_plan_name = serializers.SerializerMethodField()
    keyword = serializers.CharField(source='keyword.keyword', read_only=True)
    context = serializers.SerializerMethodField()

    class Meta:
        model = Article
        fields = ['title', 'article_uid', 'user_email', 'user_id', 'user_plan_name', 'keyword', 'context', 'created_on']

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._product_name_map = None

    def get_context(self, obj):
        return "Yes" if obj.context else "No"

    def get_user_plan_name(self, obj):
        # Initialize the product name map if not already done
        if self._product_name_map is None:
            self._initialize_product_name_map()

        return self._product_name_map.get(obj.user.stripe_product_id, "Not Found")

    def _initialize_product_name_map(self):
        """Initialize the product name map for batch processing"""
        if self.instance and hasattr(self.instance, '__iter__'):
            product_ids = set()
            for article in self.instance:
                if hasattr(article, 'user') and article.user and article.user.stripe_product_id:
                    product_ids.add(article.user.stripe_product_id)

            # Get all plan names in one batch
            self._product_name_map = get_user_plan_names_batch(list(product_ids))

        else:
            if (self.instance and hasattr(self.instance, 'user') and
                self.instance.user and self.instance.user.stripe_product_id):
                product_ids = [self.instance.user.stripe_product_id]
                self._product_name_map = get_user_plan_names_batch(product_ids)
            else:
                self._product_name_map = {}


class WebPageSerializer(serializers.ModelSerializer):
    lastScanned = serializers.DateTimeField(source="last_scraped_on", read_only=True)
    includeLinking = serializers.CharField(source="include_linking", read_only=True)
    schemaFound = serializers.BooleanField(source="schema_found", read_only=True)
    autoSchemaEnabled = serializers.BooleanField(source="schema_enabled", read_only=True)
    schemaGenerated = serializers.BooleanField(source="schema_generated", read_only=True)
    schemaGenerating = serializers.BooleanField(source="schema_generating", read_only=True)
    schemaGenerationFailed = serializers.BooleanField(source="schema_generation_failed", read_only=True)

    class Meta:
        model = WebPage
        fields = [
            'url',
            'title',
            'summary',
            'lastScanned',
            'includeLinking',
            'schema',
            'schemaFound',
            'autoSchemaEnabled',
            'schemaGenerated',
            'schemaGenerating',
            'schemaGenerationFailed',
        ]


class BlogFinderSerializer(serializers.ModelSerializer):
    blogUrl = serializers.CharField(source='blog_url')
    isValid = serializers.BooleanField(source='is_valid')
    authorName = serializers.CharField(source='author_name')
    emailAddress = serializers.CharField(source='email_address')
    createdOn = serializers.DateTimeField(source='created_at')

    class Meta:
        model = BlogFinder
        fields = ['blogUrl', 'isValid', 'authorName', 'emailAddress', 'createdOn']


class BlogFinderProjectSerializer(serializers.ModelSerializer):
    blogProjectName = serializers.CharField(source='blog_project_name')
    blogProjectId = serializers.CharField(source='blog_project_id')
    createdOn = serializers.DateTimeField(source='created_at')

    class Meta:
        model = BlogFinderProject
        fields = ['blogProjectName',"blogProjectId","createdOn"]


class GlossaryContentSerializer(serializers.ModelSerializer):
    class Meta:
        model = GlossaryContent
        fields = [
            'id',
            'website',
            'user',
            'project_id',
            'topic',
            'term',
            'content',
            'keyword_hash',
            'internal_link_count',
            'glossary_link',
            'is_processing',
            'is_failed',
            'is_generated',
            'is_posted',
            'is_archived',
            'posted_to',
            'created_at',
            'generated_on',
            'posted_on',
            'feedback',
            'task_id',
        ]


class GlossaryTopicSerializer(serializers.ModelSerializer):
    class Meta:
        model = GlossaryContent
        fields = [
            'term',
            'keyword_hash',
            'content',
            'task_id',
            'is_posted',
            'glossary_link',
            'is_archived',
        ]


class GuestPostFinderQuerySerializer(serializers.ModelSerializer):
    class Meta:
        model = GuestPostFinderQuery
        fields = ['guest_project_id', 'query', 'limit', 'is_processing', 'created_at']


class HypestatDataSerializer(serializers.ModelSerializer):
    class Meta:
        model = HypestatData
        fields = ['domain', 'organic_traffic', 'organic_keywords',
                  'domain_authority', 'total_backlinks', 'follow',
                  'no_follow', 'referring_domains']


class GuestPostFinderResultSerializer(serializers.ModelSerializer):
    hypestat = HypestatDataSerializer()
    query = serializers.CharField(source='guest_post_finder.query')

    class Meta:
        model = GuestPostFinderResult
        fields = ['post_title', 'post_link', 'hypestat', 'query']

class RedditPostFinderQuerySerializer(serializers.ModelSerializer):
    class Meta:
        model = RedditPostFinderQuery
        fields = ['reddit_project_id', 'query', 'limit', 'is_processing', 'created_at']

class RedditPostFinderResultSerializer(serializers.ModelSerializer):
    query = serializers.CharField(source='reddit_post_finder.query')

    class Meta:
        model = RedditPostFinderResult
        fields = ['post_title', 'post_link', 'position', 'query', 'subreddit_name', 'upvote_score', 'num_comments', 'subreddit_subscribers']


class ChangeLogSerializer(serializers.ModelSerializer):
    class Meta:
        model = ChangeLog
        fields = ["id", "title", "description", "created_at"]


class AICalculatorSerializer(serializers.ModelSerializer):
    status = serializers.CharField(source="generation_status", read_only=True)
    class Meta:
        model = AICalculator
        fields = ["calculator_id", "calc_type", "calc_description", "created_on", "status"]

class GHLSitesSerializer(serializers.ModelSerializer):
    class Meta:
        model = GHLIntegration
        fields = ['location_id', 'site_id', 'ghl_domain']  

class GoogleSerializer(serializers.ModelSerializer):
    verified_domains = serializers.ListField(child=serializers.CharField(), read_only=True)

    class Meta:
        model = GoogleIntegration
        fields = ["integration_type", "verified_domains"]

    def to_representation(self, instance):
        data = super().to_representation(instance)

        # Attach the verified_domains from context to each instance
        verified_domains = self.context.get("verified_domains", [])

        # You can customize here: e.g. attach all, filter by domain, etc.
        data["verified_domains"] = verified_domains

        return data
            
class GHLCategoriesSerializer(serializers.Serializer):
    id = serializers.CharField(source="_id")  # Map "_id" to "id"
    name = serializers.CharField(source="label")  
    
class GhlSaveCategoriesSerializer(serializers.ModelSerializer):
    """
    Serializer for the GhlCategories model.
    """
    class Meta:
        model = GHLCategories
        fields = ['article_title', 'category_id']
        
class WordpressPublishedArticleSerializer(serializers.ModelSerializer):
    article_uid = serializers.CharField(source="article.article_uid", read_only=True)
    word_count = serializers.CharField(source="article.word_count", read_only=True)
    internal_link_count = serializers.CharField(source="article.internal_link_count", read_only=True)
    external_link_count = serializers.CharField(source="article.external_link_count", read_only=True)
    post_link = serializers.CharField(source="article.article_link", read_only=True)
    posted_on = serializers.CharField(source="article.posted_on", read_only=True)
    
    class Meta:
        model = WordpressPublishedArticle
        fields = [
            "id",
            "post_id",
            "media_id",
            "title",
            "slug",
            "url",
            "published_date",
            "gsc_position",
            "article_uid",            
            "word_count",
            "internal_link_count",
            "external_link_count",
            "post_link",
            "posted_on"
        ]


class AIStatsPageVersionSerializer(serializers.ModelSerializer):
    """Serializer for AIStatsPageVersion model"""
    created_on_relative = serializers.SerializerMethodField()
    code_length = serializers.ReadOnlyField()
    code_preview = serializers.ReadOnlyField()
    
    class Meta:
        model = AIStatsPageVersion
        fields = [
            'id', 
            'version_name', 
            'html_code', 
            'changes_summary',
            'created_on',
            'created_on_relative',
            'code_length',
            'code_preview'
        ]
    
    def get_created_on_relative(self, obj):
        """Returns relative time like '2 hours ago'"""
        try:
            return timesince(obj.created_on, timezone.now()) + " ago"
        except:
            return "Unknown"


class AIStatsPageDetailSerializer(serializers.ModelSerializer):
    """Detailed serializer for AI stats pages with all versions"""
    versions = AIStatsPageVersionSerializer(source='aistatspageversion_set', many=True, read_only=True)
    version_count = serializers.SerializerMethodField()
    html_content = serializers.SerializerMethodField()
    created_on_relative = serializers.SerializerMethodField()
    selected_idea_title = serializers.SerializerMethodField()
    selected_idea_description = serializers.SerializerMethodField()
    
    class Meta:
        model = AIStatsPage
        fields = [
            'stats_id',
            'stats_type',
            'stats_topic', 
            'stats_description',
            'created_on',
            'created_on_relative',
            'updated_on',
            'html_content',
            'versions',
            'version_count',
            'original_keyword',
            'selected_idea',
            'selected_idea_title',
            'selected_idea_description',
            'current_version_id',
        ]
    
    def get_version_count(self, obj):
        return obj.get_version_count()
    
    def get_html_content(self, obj):
        """
        Returns HTML content from current version
        """
        current_version = obj.get_current_version()
        return current_version.html_code if current_version else ""
    
    def get_created_on_relative(self, obj):
        try:
            return timesince(obj.created_on, timezone.now()) + " ago"
        except:
            return "Unknown"
    
    def get_selected_idea_title(self, obj):
        return obj.get_selected_idea_title()
    
    def get_selected_idea_description(self, obj):
        return obj.get_selected_idea_description()


class AIStatsPageListSerializer(serializers.ModelSerializer):
    """Simplified list serializer"""
    created_on_relative = serializers.SerializerMethodField()
    version_count = serializers.SerializerMethodField()
    current_html_code = serializers.SerializerMethodField()
    selected_idea_title = serializers.SerializerMethodField()
    selected_idea_description = serializers.SerializerMethodField()
    
    class Meta:
        model = AIStatsPage
        fields = [
            'id', 
            'stats_id', 
            'stats_topic', 
            'stats_type', 
            'stats_description', 
            'original_keyword',
            'selected_idea',
            'selected_idea_title',
            'selected_idea_description',
            'created_on', 
            'created_on_relative', 
            'version_count',
            'current_html_code',  
            'script_tag', 
            'div_tag',
        ]
    
    def get_selected_idea_title(self, obj):
        return obj.get_selected_idea_title()
    
    def get_selected_idea_description(self, obj):
        return obj.get_selected_idea_description()
    
    def get_created_on_relative(self, obj):
        from django.utils import timezone
        
        now = timezone.now()
        diff = now - obj.created_on
        
        if diff.days > 0:
            return f"{diff.days} day{'s' if diff.days > 1 else ''} ago"
        elif diff.seconds > 3600:
            hours = diff.seconds // 3600
            return f"{hours} hour{'s' if hours > 1 else ''} ago"
        elif diff.seconds > 60:
            minutes = diff.seconds // 60
            return f"{minutes} minute{'s' if minutes > 1 else ''} ago"
        else:
            return "Just now"
    
    def get_version_count(self, obj):
        return obj.get_version_count()
    
    def get_current_html_code(self, obj):
        return obj.html_code

class AIComparisonPageVersionSerializer(serializers.ModelSerializer):
    """Serializer for AIComparisonPageVersion model"""
    created_on_relative = serializers.SerializerMethodField()
    code_length = serializers.ReadOnlyField()
    code_preview = serializers.ReadOnlyField()
    
    class Meta:
        model = AIComparisonPageVersion
        fields = [
            'id', 
            'version_name', 
            'html_code', 
            'changes_summary',
            'created_on',
            'created_on_relative',
            'code_length',
            'code_preview'
        ]
    
    def get_created_on_relative(self, obj):
        """Returns relative time like '2 hours ago'"""
        try:
            return timesince(obj.created_on, timezone.now()) + " ago"
        except:
            return "Unknown"


class AIComparisonPageSerializer(serializers.ModelSerializer):
    """
    Serializer for AI Comparison Pages
    """
    created_on_relative = serializers.SerializerMethodField()
    updated_on_relative = serializers.SerializerMethodField()
    version_count = serializers.SerializerMethodField()
    current_version = serializers.SerializerMethodField()
    html_code = serializers.ReadOnlyField()
    script_tag = serializers.ReadOnlyField()
    div_tag = serializers.ReadOnlyField()
    
    latest_version = AIComparisonPageVersionSerializer(source='get_current_version', read_only=True)

    class Meta:
        model = AIComparisonPage
        fields = [
            'id',
            'comp_id',
            'comparison_type',
            'url1',
            'url2',
            'html_code',
            'script_tag',
            'div_tag',
            'is_verified',
            'version_count',
            'current_version',
            'latest_version',
            'created_on',
            'updated_on',
            'created_on_relative',
            'updated_on_relative',
        ]
        read_only_fields = [
            'id', 
            'comp_id', 
            'html_code', 
            'script_tag', 
            'div_tag',
            'created_on', 
            'updated_on'
        ]

    def get_created_on_relative(self, obj):
        """Get relative time string for creation date"""
        from mainapp.utils import get_relative_time
        return get_relative_time(obj.created_on)

    def get_updated_on_relative(self, obj):
        """Get relative time string for update date"""
        from mainapp.utils import get_relative_time
        return get_relative_time(obj.updated_on)

    def get_version_count(self, obj):
        """Get total number of versions"""
        return obj.get_version_count()

    def get_current_version(self, obj):
        """Get current version name"""
        current_version = obj.get_current_version()
        return current_version.version_name if current_version else "v1"


class AIComparisonPageDetailSerializer(serializers.ModelSerializer):
    """Detailed serializer for AI comparison pages with all versions"""
    versions = AIComparisonPageVersionSerializer(source='aicomparisonpageversion_set', many=True, read_only=True)
    version_count = serializers.SerializerMethodField()
    html_content = serializers.SerializerMethodField()
    created_on_relative = serializers.SerializerMethodField()
    
    class Meta:
        model = AIComparisonPage
        fields = [
            'comp_id',
            'comparison_type',
            'url1',
            'url2',
            'created_on',
            'created_on_relative',
            'updated_on',
            'html_content',
            'versions',
            'version_count',
            'current_version_id',
        ]
    
    def get_version_count(self, obj):
        return obj.get_version_count()
    
    def get_html_content(self, obj):
        """
        Returns HTML content from current version based on current_version_id
        This is the key fix - it now respects the saved current_version_id
        """
        current_version = obj.get_current_version()
        return current_version.html_code if current_version else ""
    
    def get_created_on_relative(self, obj):
        try:
            return timesince(obj.created_on, timezone.now()) + " ago"
        except:
            return "Unknown"
        

class AIComparisonPageListSerializer(serializers.ModelSerializer):
    """Enhanced list serializer for comparison pages"""
    created_on_relative = serializers.SerializerMethodField()
    version_count = serializers.SerializerMethodField()
    current_html_code = serializers.SerializerMethodField()
    url1_domain = serializers.SerializerMethodField()
    url2_domain = serializers.SerializerMethodField()
    
    class Meta:
        model = AIComparisonPage
        fields = [
            'id', 'comp_id', 'comparison_type', 'url1', 'url2', 
            'url1_domain', 'url2_domain', 
            'is_verified', 'created_on', 'created_on_relative', 
            'version_count', 'current_html_code', 'verification_token', 
            'script_tag', 'div_tag'
        ]
    
    def get_url1_domain(self, obj):
        """Extract domain from URL1"""
        try:
            from urllib.parse import urlparse
            return urlparse(obj.url1).netloc.replace('www.', '')
        except:
            return obj.url1
    
    def get_url2_domain(self, obj):
        """Extract domain from URL2"""
        try:
            from urllib.parse import urlparse
            return urlparse(obj.url2).netloc.replace('www.', '')
        except:
            return obj.url2
    
    def get_created_on_relative(self, obj):
        from django.utils import timezone
        from datetime import datetime, timedelta
        
        now = timezone.now()
        diff = now - obj.created_on
        
        if diff.days > 0:
            return f"{diff.days} day{'s' if diff.days > 1 else ''} ago"
        elif diff.seconds > 3600:
            hours = diff.seconds // 3600
            return f"{hours} hour{'s' if hours > 1 else ''} ago"
        elif diff.seconds > 60:
            minutes = diff.seconds // 60
            return f"{minutes} minute{'s' if minutes > 1 else ''} ago"
        else:
            return "Just now"
    
    def get_version_count(self, obj):
        return obj.get_version_count()
    
    def get_current_html_code(self, obj):
        return obj.html_code

class AIShareWidgetSerializer(serializers.ModelSerializer):
    total_clicks_by_llm = serializers.SerializerMethodField()
    destination_urls_stats = serializers.SerializerMethodField()
    
    class Meta:
        model = AIShareWidget
        fields = [
            'id', 'widget_id', 'name', 'prompt_template', 'text_before_button',
            'selected_llms', 'style', 'html_code', 'html_code_dark', 'is_active', 'is_processing',
            'total_clicks', 'created_at', 'updated_at', 'total_clicks_by_llm',
            'destination_urls_stats'
        ]
    
    def get_total_clicks_by_llm(self, obj):
        """Get total clicks grouped by LLM"""
        clicks_by_llm = {}
        for click in obj.clicks.values('llm').annotate(count=models.Count('id')):
            clicks_by_llm[click['llm']] = click['count']
        return clicks_by_llm
    
    def get_destination_urls_stats(self, obj):
        """Get destination URLs with click counts by LLM"""
        url_stats = {}
        clicks = obj.clicks.values('destination_url', 'llm').annotate(count=models.Count('id'))
        
        for click in clicks:
            url = click['destination_url']
            llm = click['llm']
            count = click['count']
            
            if url not in url_stats:
                url_stats[url] = {'total': 0, 'by_llm': {}}
            
            url_stats[url]['by_llm'][llm] = count
            url_stats[url]['total'] += count
        
        return url_stats
