(function() {
    'use strict';

    // Dynamic backend URL from server
    var BACKEND_URL = '{{BACKEND_URL}}';
    
    // Get backend URL 
    function getBackendUrl() {
        return BACKEND_URL;
    }
    
    // Get proper current page URL, excluding unwanted URLs
    function getCurrentPageUrl() {
        var currentUrl = window.location.href;
        var unwantedUrls = [
            'about:srcdoc',
            'about:blank',
            'data:',
            'javascript:',
            'chrome-extension:',
            'moz-extension:',
            'safari-extension:'
        ];
        
        for (var i = 0; i < unwantedUrls.length; i++) {
            if (currentUrl.indexOf(unwantedUrls[i]) === 0) {
                try {
                    if (window.parent && window.parent !== window) {
                        currentUrl = window.parent.location.href;
                    } else if (window.top && window.top !== window) {
                        currentUrl = window.top.location.href;
                    }
                } catch (e) {
                    console.warn('Cannot access parent URL due to cross-origin restrictions');
                    currentUrl = '';
                }
                break;
            }
        }
        
        if (currentUrl.indexOf('file://') === 0 || currentUrl.indexOf('about:') === 0 || currentUrl === '') {
            return '';
        }
        
        return currentUrl;
    }
    
    // Build redirect URL through our backend
    function buildRedirectUrl(widgetId, llm, currentUrl) {
        var backendUrl = getBackendUrl();
        var redirectUrl = backendUrl + '/api/frontend/share/';
        var params = new URLSearchParams();
        params.set('id', widgetId);
        params.set('llm', llm);
        
        if (currentUrl && currentUrl.trim() !== '') {
            params.set('url', currentUrl);
        } else {
            console.log('Skipping URL parameter due to invalid/empty URL');
        }
        
        return redirectUrl + '?' + params.toString();
    }
    
    // Global function to handle LLM clicks
    window.handleLLMClick = function(widgetId, llm) {
        if (!widgetId || !llm) {
            console.error('Missing widget data!');
            return;
        }

        var currentUrl = getCurrentPageUrl();
        var redirectUrl = buildRedirectUrl(widgetId, llm, currentUrl);
        
        window.open(redirectUrl, '_blank');
    };

})();
